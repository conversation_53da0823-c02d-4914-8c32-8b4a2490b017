from fastapi import FastAP<PERSON>, HTTPException
from fastapi.exceptions import RequestValidationError
import logging
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import presentation layer
from src.presentation.controllers.media_controller import router
from src.infrastructure.config.settings import settings
from src.presentation.middleware.error_handler import (
    ErrorHandlerMiddleware,
    handle_validation_error,
    handle_http_exception
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('error.logs'),
        logging.StreamHandler()  # Keep console output as well
    ]
)
logger = logging.getLogger(__name__)

# Ensure required directories exist
settings.ensure_directories()

app = FastAPI(title="InstaSaver API", version="1.0.0")

# Add error handling middleware
app.add_middleware(ErrorHandlerMiddleware)

# Add exception handlers
app.add_exception_handler(RequestValidationError, handle_validation_error)
app.add_exception_handler(HTTPException, handle_http_exception)

# Include the API router
app.include_router(router)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app, 
        host=settings.server.host, 
        port=settings.server.port,
        reload=settings.server.reload
    )
