"""Interface for media cache repository."""
from abc import ABC, abstractmethod
from typing import Optional
from src.domain.entities.media_cache import MediaCache


class IMediaCacheRepository(ABC):
    """Interface for media cache repository operations."""

    @abstractmethod
    async def save_media_cache(self, media_cache: MediaCache) -> None:
        """
        Save media cache entry.

        Args:
            media_cache: MediaCache entity to save
        """
        pass

    @abstractmethod
    async def get_cached_media(
        self,
        url: str,
        bot_id: str,
        media_type: str
    ) -> Optional[MediaCache]:
        """
        Get cached media by URL, bot_id and media_type.

        Args:
            url: Media URL
            bot_id: Bot identifier
            media_type: Type of media ("audio", "video", or "image")

        Returns:
            MediaCache if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def cleanup_expired_cache(self, days: int = 30) -> int:
        """
        Clean up expired cache entries.
        
        Args:
            days: Number of days after which cache expires
            
        Returns:
            Number of deleted entries
        """
        pass
    
    @abstractmethod
    async def get_cache_stats(self) -> dict:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        pass
