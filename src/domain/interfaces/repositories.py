"""Repository interfaces."""
from abc import ABC, abstractmethod
from typing import List, Optional
from datetime import datetime


from ..value_objects.identifiers import UserId
from ..value_objects.url import Url


class IHistoryRepository(ABC):
    """Interface for history repository."""

    @abstractmethod
    async def save_download_history(
        self, 
        user_id: UserId, 
        url: Url, 
        telegram_file_id: str,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Save download history."""
        pass

    @abstractmethod
    async def get_user_history(self, user_id: UserId, limit: int = 10) -> List[dict]:
        """Get user download history."""
        pass

    @abstractmethod
    async def url_exists_in_history(self, url: Url) -> bool:
        """Check if URL exists in history."""
        pass


class IMediaRepository(ABC):
    """Interface for media repository."""

    @abstractmethod
    async def save_file(self, file_path: str, content: bytes) -> None:
        """Save file to storage."""
        pass

    @abstractmethod
    async def delete_file(self, file_path: str) -> None:
        """Delete file from storage."""
        pass

    @abstractmethod
    async def file_exists(self, file_path: str) -> bool:
        """Check if file exists."""
        pass

    @abstractmethod
    async def get_file_size(self, file_path: str) -> int:
        """Get file size in bytes."""
        pass



