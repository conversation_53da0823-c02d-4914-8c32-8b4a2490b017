"""Utility functions for extracting request and response details."""
import json
import logging
from typing import Dict, Any, Optional
from fastapi import Request
from fastapi.responses import Response

logger = logging.getLogger(__name__)


def extract_response_details(response: Response) -> Dict[str, Any]:
    """
    Extract response details for error logging.
    
    Args:
        response: FastAPI response object
        
    Returns:
        Dictionary containing response details
    """
    try:
        details = {
            'status_code': getattr(response, 'status_code', None),
            'headers': dict(getattr(response, 'headers', {}))
        }
        
        # Try to get response body
        try:
            if hasattr(response, 'body'):
                body = response.body
                if body:
                    # Try to parse as JSON first
                    try:
                        body_json = json.loads(body.decode('utf-8'))
                        details['response_body'] = body_json
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        # If not JSON, store as string
                        details['response_body'] = body.decode('utf-8') if isinstance(body, bytes) else str(body)
        except Exception as e:
            details['response_body'] = f"Unable to read response body: {e}"
        
        return details
    except Exception as e:
        logger.error(f"Error extracting response details: {e}")
        return {'error': f"Failed to extract response details: {e}"}


def get_client_ip(request: Request) -> str:
    """
    Get client IP address from request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Client IP address
    """
    try:
        # Check for forwarded headers first (for proxy/load balancer setups)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        # Check for real IP header
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Check for CF-Connecting-IP (Cloudflare)
        cf_ip = request.headers.get("CF-Connecting-IP")
        if cf_ip:
            return cf_ip
        
        # Fallback to client host
        return request.client.host if request.client else "Unknown"
    except Exception:
        return "Unknown"


async def extract_bot_token(request: Request) -> Optional[str]:
    """
    Extract bot token from request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Bot token if found, None otherwise
    """
    try:
        # Try to get bot token from headers
        bot_token = request.headers.get("X-Bot-Token") or request.headers.get("Authorization")
        if bot_token and bot_token.startswith("Bearer "):
            bot_token = bot_token[7:]  # Remove "Bearer " prefix
        
        # If not in headers, try to get from query params
        if not bot_token:
            bot_token = request.query_params.get("bot_token")
        
        # If still not found, try to parse from body for POST requests
        if not bot_token and request.method == "POST":
            try:
                body_bytes = await request.body()
                if body_bytes:
                    body_json = json.loads(body_bytes.decode('utf-8'))
                    bot_token = body_json.get("bot_token")
            except Exception:
                pass
        
        return bot_token if bot_token else None
    except Exception as e:
        logger.error(f"Error extracting bot token: {e}")
        return None


async def extract_user_chat_id(request: Request) -> Optional[str]:
    """
    Extract user chat ID from request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        User chat ID if found, None otherwise
    """
    try:
        # Try to get from headers
        chat_id = request.headers.get("X-Chat-ID")
        
        # If not in headers, try query params
        if not chat_id:
            chat_id = request.query_params.get("chat_id")
        
        # If still not found, try to parse from body for POST requests
        if not chat_id and request.method == "POST":
            try:
                body_bytes = await request.body()
                if body_bytes:
                    body_json = json.loads(body_bytes.decode('utf-8'))
                    chat_id = body_json.get("chat_id")
            except Exception:
                pass
        
        return chat_id if chat_id else None
    except Exception as e:
        logger.error(f"Error extracting user chat ID: {e}")
        return None


def sanitize_headers(headers: Dict[str, str]) -> Dict[str, str]:
    """
    Sanitize headers by removing sensitive information.
    
    Args:
        headers: Original headers dictionary
        
    Returns:
        Sanitized headers dictionary
    """
    sensitive_headers = {
        'authorization', 'cookie', 'x-api-key', 'x-auth-token',
        'x-bot-token', 'x-secret-key', 'x-access-token'
    }
    
    return {k: v for k, v in headers.items() 
            if k.lower() not in sensitive_headers}


def truncate_string(text: str, max_length: int = 200) -> str:
    """
    Truncate string if it exceeds maximum length.
    
    Args:
        text: Text to truncate
        max_length: Maximum length before truncation
        
    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    return text[:max_length] + "..." 