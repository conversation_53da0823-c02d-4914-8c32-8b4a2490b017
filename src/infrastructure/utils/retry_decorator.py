"""Retry decorator for handling API failures with exponential backoff."""
import asyncio
import logging
from functools import wraps
from typing import Callable, Any, Type, Tuple
import httpx

logger = logging.getLogger(__name__)


def retry_async(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    exceptions: Tuple[Type[Exception], ...] = (httpx.RequestError, httpx.HTTPStatusError, ConnectionError, TimeoutError)
):
    """
    Async retry decorator with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts (default: 3)
        delay: Initial delay between retries in seconds (default: 1.0)
        backoff: Backoff multiplier for delay (default: 2.0)
        exceptions: Tuple of exception types to retry on
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    result = await func(*args, **kwargs)
                    if attempt > 0:
                        logger.info(f"Function {func.__name__} succeeded on attempt {attempt + 1}")
                    return result
                    
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Last attempt failed, log and re-raise
                        logger.error(f"Function {func.__name__} failed after {max_attempts} attempts. Last error: {e}")
                        raise e
                    
                    # Log retry attempt
                    logger.warning(f"Function {func.__name__} failed on attempt {attempt + 1}/{max_attempts}. Error: {e}. Retrying in {current_delay}s...")
                    
                    # Wait before retry
                    await asyncio.sleep(current_delay)
                    current_delay *= backoff
                    
                except Exception as e:
                    # For non-retryable exceptions, fail immediately
                    logger.error(f"Function {func.__name__} failed with non-retryable exception: {e}")
                    raise e
            
            # This should never be reached, but just in case
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator


def retry_sync(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    exceptions: Tuple[Type[Exception], ...] = (ConnectionError, TimeoutError)
):
    """
    Sync retry decorator with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts (default: 3)
        delay: Initial delay between retries in seconds (default: 1.0)
        backoff: Backoff multiplier for delay (default: 2.0)
        exceptions: Tuple of exception types to retry on
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            import time
            
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    result = func(*args, **kwargs)
                    if attempt > 0:
                        logger.info(f"Function {func.__name__} succeeded on attempt {attempt + 1}")
                    return result
                    
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Last attempt failed, log and re-raise
                        logger.error(f"Function {func.__name__} failed after {max_attempts} attempts. Last error: {e}")
                        raise e
                    
                    # Log retry attempt
                    logger.warning(f"Function {func.__name__} failed on attempt {attempt + 1}/{max_attempts}. Error: {e}. Retrying in {current_delay}s...")
                    
                    # Wait before retry
                    time.sleep(current_delay)
                    current_delay *= backoff
                    
                except Exception as e:
                    # For non-retryable exceptions, fail immediately
                    logger.error(f"Function {func.__name__} failed with non-retryable exception: {e}")
                    raise e
            
            # This should never be reached, but just in case
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator
