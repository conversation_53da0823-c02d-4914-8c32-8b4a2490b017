"""Application configuration settings."""
from typing import Optional
from dataclasses import dataclass
from pathlib import Path
from environs import Env


@dataclass
class FileStorageConfig:
    """File storage configuration."""
    downloads_dir: str = "downloads"
    max_file_size: int = 50 * 1024 * 1024  # 50MB


@dataclass
class TelegramConfig:
    """Telegram configuration."""
    local_api_url: str = "http://localhost:8081/bot{0}/{1}"
    connection_timeout: int = 300
    read_timeout: int = 300
    # Telegram API limits
    max_caption_length: int = 1024  # Maximum caption length for media
    max_message_length: int = 4096  # Maximum message length


@dataclass
class InstagramConfig:
    """Instagram API configuration."""
    api_base_url: str = "https://fastsaverapi.com/get-info"
    api_token: str = ""


@dataclass
class MusicSearchConfig:
    """Music search API configuration."""
    api_base_url: str = "https://fastsaverapi.com/search-music"
    get_info_url: str = "https://fastsaverapi.com/get-info"
    api_token: str = "lxcMy0OtNaimyGEQkdHjXAmC"


@dataclass
class YouTubeConfig:
    """YouTube download configuration using FastSaver API only."""
    fastsaver_api_token: str = "lxcMy0OtNaimyGEQkdHjXAmC"
    fastsaver_download_url: str = "https://fastsaverapi.com/download"
    fastsaver_get_info_url: str = "https://fastsaverapi.com/get-info"
    default_video_format: str = "720p"  # Default video format for FastSaver API
    available_formats: list = None  # Will be set in __post_init__

    def __post_init__(self):
        """Initialize available formats."""
        if self.available_formats is None:
            self.available_formats = ["144p", "240p", "360p", "480p", "720p", "1080p", "1440p", "2160p", "mp3"]


@dataclass
class PinterestConfig:
    """Pinterest API configuration."""
    api_base_url: str = "https://fastsaverapi.com/get-info"
    api_token: str = "lxcMy0OtNaimyGEQkdHjXAmC"


@dataclass
class TikTokConfig:
    """TikTok API configuration."""
    api_base_url: str = "https://fastsaverapi.com/get-info"
    api_token: str = "lxcMy0OtNaimyGEQkdHjXAmC"


@dataclass
class AdminConfig:
    """Admin configuration."""
    chat_id: Optional[str] = None
    enable_error_notifications: bool = True


@dataclass
class ServerConfig:
    """Server configuration."""
    host: str = "0.0.0.0"
    port: int = 3333
    debug: bool = False
    reload: bool = False


@dataclass
class Settings:
    """Application settings."""
    file_storage: FileStorageConfig
    telegram: TelegramConfig
    instagram: InstagramConfig
    music_search: MusicSearchConfig
    youtube: YouTubeConfig
    pinterest: PinterestConfig
    tiktok: TikTokConfig
    server: ServerConfig
    admin: AdminConfig

    @classmethod
    def from_env(cls) -> "Settings":
        """Create settings from environment variables using environs."""
        env = Env()
        
        return cls(
            file_storage=FileStorageConfig(
                downloads_dir=env.str("DOWNLOADS_DIR", default="downloads"),
                max_file_size=env.int("MAX_FILE_SIZE", default=52428800)  # 50MB default
            ),
            telegram=TelegramConfig(
                local_api_url=env.str("TELEGRAM_LOCAL_API_URL", default="http://localhost:8081/bot{0}/{1}"),
                connection_timeout=env.int("TELEGRAM_CONNECTION_TIMEOUT", default=300),
                read_timeout=env.int("TELEGRAM_READ_TIMEOUT", default=300)
            ),
            instagram=InstagramConfig(
                api_base_url=env.str("INSTAGRAM_API_BASE_URL", default="https://fastsaverapi.com/get-info"),
                api_token=env.str("INSTAGRAM_API_TOKEN", default="")
            ),
            music_search=MusicSearchConfig(
                api_base_url=env.str("MUSIC_SEARCH_API_BASE_URL", default="https://fastsaverapi.com/search-music"),
                get_info_url=env.str("MUSIC_SEARCH_GET_INFO_URL", default="https://fastsaverapi.com/get-info"),
                api_token=env.str("MUSIC_SEARCH_API_TOKEN", default="lxcMy0OtNaimyGEQkdHjXAmC")
            ),
            youtube=YouTubeConfig(
                fastsaver_api_token=env.str("YOUTUBE_FASTSAVER_API_TOKEN", default="lxcMy0OtNaimyGEQkdHjXAmC"),
                fastsaver_download_url=env.str("YOUTUBE_FASTSAVER_DOWNLOAD_URL", default="https://fastsaverapi.com/download"),
                fastsaver_get_info_url=env.str("YOUTUBE_FASTSAVER_GET_INFO_URL", default="https://fastsaverapi.com/get-info"),
                default_video_format=env.str("YOUTUBE_DEFAULT_VIDEO_FORMAT", default="720p"),
            ),
            pinterest=PinterestConfig(
                api_base_url=env.str("PINTEREST_API_BASE_URL", default="https://fastsaverapi.com/get-info"),
                api_token=env.str("PINTEREST_API_TOKEN", default="lxcMy0OtNaimyGEQkdHjXAmC")
            ),
            tiktok=TikTokConfig(
                api_base_url=env.str("TIKTOK_API_BASE_URL", default="https://fastsaverapi.com/get-info"),
                api_token=env.str("TIKTOK_API_TOKEN", default="lxcMy0OtNaimyGEQkdHjXAmC")
            ),
            server=ServerConfig(
                host=env.str("SERVER_HOST", default="0.0.0.0"),
                port=env.int("SERVER_PORT", default=3333),
                debug=env.bool("DEBUG", default=False),
                reload=env.bool("RELOAD", default=False)
            ),
            admin=AdminConfig(
                chat_id=env.str("ADMIN_CHAT_ID", default=2105729169),
                enable_error_notifications=env.bool("ADMIN_ENABLE_ERROR_NOTIFICATIONS", default=True)
            )
        )

    def ensure_directories(self):
        """Ensure required directories exist."""
        Path(self.file_storage.downloads_dir).mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings.from_env() 