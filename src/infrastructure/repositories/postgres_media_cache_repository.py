"""PostgreSQL implementation of media cache repository."""
import logging
import asyncpg
from datetime import datetime, timedelta
from typing import Optional

from src.domain.interfaces.media_cache_repository import IMediaCacheRepository
from src.domain.entities.media_cache import MediaCache
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class PostgresMediaCacheRepository(IMediaCacheRepository):
    """PostgreSQL implementation of media cache repository."""

    def __init__(self, connection_pool=None):
        if asyncpg is None:
            raise ImportError("asyncpg is required for PostgreSQL support. Install with: pip install asyncpg")
        self.connection_pool = connection_pool
        self._connection_string = self._build_connection_string()
        self._initialized = False

    def _build_connection_string(self) -> str:
        """Build PostgreSQL connection string."""
        if settings.database.url:
            return settings.database.url
        
        # Fallback to default if no URL provided
        return "postgresql://saverbot:password@127.0.0.1:5432/saverbot"

    async def _get_connection(self):
        """Get database connection."""
        if self.connection_pool:
            return await self.connection_pool.acquire()
        return await asyncpg.connect(self._connection_string)

    async def _release_connection(self, conn):
        """Release database connection."""
        if self.connection_pool:
            await self.connection_pool.release(conn)
        else:
            await conn.close()

    async def init_db(self):
        """Initialize the database tables."""
        conn = await self._get_connection()
        try:
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS media_cache (
                    id SERIAL PRIMARY KEY,
                    url TEXT NOT NULL,
                    bot_id TEXT NOT NULL,
                    file_id TEXT NOT NULL,
                    media_type TEXT NOT NULL CHECK (media_type IN ('audio', 'video', 'image')),
                    title TEXT,
                    duration INTEGER,
                    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(url, bot_id, media_type)
                )
            ''')
            
            # Create indexes for better performance
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_media_cache_lookup 
                ON media_cache(url, bot_id, media_type)
            ''')
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_media_cache_cached_at 
                ON media_cache(cached_at)
            ''')
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_media_cache_bot_id 
                ON media_cache(bot_id)
            ''')
            
            logger.info("Media cache table initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing media cache table: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def _ensure_initialized(self):
        """Ensure database is initialized."""
        if not self._initialized:
            await self.init_db()
            self._initialized = True

    async def save_media_cache(self, media_cache: MediaCache) -> None:
        """Save media cache entry."""
        await self._ensure_initialized()
        conn = await self._get_connection()
        try:
            # Use INSERT ... ON CONFLICT to update existing entries
            await conn.execute(
                """INSERT INTO media_cache
                   (url, bot_id, file_id, media_type, title, duration, cached_at)
                   VALUES ($1, $2, $3, $4, $5, $6, $7)
                   ON CONFLICT (url, bot_id, media_type)
                   DO UPDATE SET
                       file_id = EXCLUDED.file_id,
                       title = EXCLUDED.title,
                       duration = EXCLUDED.duration,
                       cached_at = EXCLUDED.cached_at""",
                media_cache.url,
                media_cache.bot_id,
                media_cache.file_id,
                media_cache.media_type,
                media_cache.title,
                media_cache.duration,
                media_cache.cached_at
            )
            
            logger.info(f"Cached media: {media_cache.cache_key}")
            
        except Exception as e:
            logger.error(f"Error saving media cache: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def get_cached_media(
        self,
        url: str,
        bot_id: str,
        media_type: str
    ) -> Optional[MediaCache]:
        """Get cached media by URL, bot_id and media_type."""
        await self._ensure_initialized()
        conn = await self._get_connection()
        try:
            # Check if cache is still valid (30 days)
            expiry_time = datetime.now() - timedelta(days=30)

            result = await conn.fetchrow(
                """SELECT url, bot_id, file_id, media_type, title, duration, cached_at
                   FROM media_cache
                   WHERE url = $1 AND bot_id = $2 AND media_type = $3 AND cached_at > $4""",
                url, bot_id, media_type, expiry_time
            )

            if result:
                logger.info(f"Found cached media: {url}:{bot_id}:{media_type}")
                return MediaCache(
                    url=result['url'],
                    bot_id=result['bot_id'],
                    file_id=result['file_id'],
                    media_type=result['media_type'],
                    title=result['title'],
                    duration=result['duration'],
                    cached_at=result['cached_at']
                )

            return None

        except Exception as e:
            logger.error(f"Error getting cached media: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def cleanup_expired_cache(self, days: int = 30) -> int:
        """Clean up expired cache entries."""
        await self._ensure_initialized()
        conn = await self._get_connection()
        try:
            expiry_time = datetime.now() - timedelta(days=days)
            
            result = await conn.execute(
                """DELETE FROM media_cache WHERE cached_at < $1""",
                expiry_time
            )
            
            # Extract number of deleted rows from result
            deleted_count = int(result.split()[-1]) if result else 0
            
            logger.info(f"Cleaned up {deleted_count} expired media cache entries")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error cleaning up expired media cache: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def get_cache_stats(self) -> dict:
        """Get cache statistics."""
        await self._ensure_initialized()
        conn = await self._get_connection()
        try:
            # Get total count
            total_result = await conn.fetchrow("SELECT COUNT(*) as total FROM media_cache")
            total_count = total_result['total'] if total_result else 0
            
            # Get count by media type
            audio_result = await conn.fetchrow(
                "SELECT COUNT(*) as count FROM media_cache WHERE media_type = 'audio'"
            )
            audio_count = audio_result['count'] if audio_result else 0
            
            video_result = await conn.fetchrow(
                "SELECT COUNT(*) as count FROM media_cache WHERE media_type = 'video'"
            )
            video_count = video_result['count'] if video_result else 0
            
            # Get count of entries from last 24 hours
            recent_time = datetime.now() - timedelta(days=1)
            recent_result = await conn.fetchrow(
                "SELECT COUNT(*) as recent FROM media_cache WHERE cached_at > $1",
                recent_time
            )
            recent_count = recent_result['recent'] if recent_result else 0
            
            # Get oldest entry
            oldest_result = await conn.fetchrow(
                "SELECT MIN(cached_at) as oldest FROM media_cache"
            )
            oldest_entry = oldest_result['oldest'] if oldest_result else None
            
            return {
                'total_entries': total_count,
                'audio_entries': audio_count,
                'video_entries': video_count,
                'recent_entries': recent_count,
                'oldest_entry': oldest_entry.isoformat() if oldest_entry else None
            }
            
        except Exception as e:
            logger.error(f"Error getting media cache stats: {e}")
            raise
        finally:
            await self._release_connection(conn)
