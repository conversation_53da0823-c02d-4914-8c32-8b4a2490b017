"""Music search service implementation."""
import httpx
import logging
from typing import Op<PERSON>

from src.domain.interfaces.services import IMusicSearchService
from src.domain.entities.music_search_result import MusicSearchResult, MusicSearchResponse
from src.domain.entities.download_result import DownloadResult
from src.infrastructure.utils.retry_decorator import retry_async
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class MusicSearchService(IMusicSearchService):
    """Music search service implementation using FastSaver API."""

    def __init__(self):
        self.api_base_url = settings.music_search.api_base_url
        self.get_info_url = settings.music_search.get_info_url
        self.api_token = settings.music_search.api_token

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def search_music(self, query: str, page: int = 1) -> MusicSearchResponse:
        """Search for music using the FastSaver API."""
        try:
            logger.info(f"Searching music with query: {query}, page: {page}")

            # Prepare API request
            params = {
                "query": query,
                "page": page,
                "token": self.api_token
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(self.api_base_url, params=params)
                response.raise_for_status()

                data = response.json()
                
                # Check for API errors
                if data.get("error", False):
                    logger.error(f"API returned error for query: {query}")
                    return MusicSearchResponse(
                        error=True,
                        page=page,
                        results=[]
                    )

                # Convert API response to domain entities and cache them
                results = []
                for item in data.get("results", []):
                    try:
                        result = MusicSearchResult(
                            title=item.get("title", ""),
                            shortcode=item.get("shortcode", ""),
                            duration=item.get("duration", ""),
                            thumb=item.get("thumb", ""),
                            thumb_best=item.get("thumb_best", "")
                        )
                        results.append(result)



                    except ValueError as e:
                        logger.warning(f"Skipping invalid search result: {e}")
                        continue

                return MusicSearchResponse(
                    error=False,
                    page=data.get("page", page),
                    results=results
                )

        except httpx.RequestError as e:
            logger.error(f"Request error during music search: {e}")
            return MusicSearchResponse(
                error=True,
                page=page,
                results=[]
            )
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error during music search: {e}")
            return MusicSearchResponse(
                error=True,
                page=page,
                results=[]
            )
        except Exception as e:
            logger.error(f"Unexpected error during music search: {e}")
            return MusicSearchResponse(
                error=True,
                page=page,
                results=[]
            )

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def download_from_shortcode(self, shortcode: str, media_type: str = "audio", bot_username: str = "instasaver_bot") -> DownloadResult:
        """Download media from YouTube using FastSaver API."""
        try:
            # Get media info from FastSaver API

            # Use FastSaver API to download
            download_url = "https://fastsaverapi.com/download"

            # Determine format based on media_type
            format_param = "mp3" if media_type == "audio" else "mp4"

            params = {
                "video_id": shortcode,
                "format": format_param,
                "bot_username": bot_username,
                "token": self.api_token
            }

            logger.info(f"Downloading from FastSaver API: {shortcode}, format: {format_param}")

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.get(download_url, params=params)
                response.raise_for_status()

                data = response.json()

                # Check for API errors
                if data.get("error", False):
                    api_error_msg = data.get('message', 'Unknown error')
                    logger.error(f"FastSaver API returned error for shortcode {shortcode}: {api_error_msg}")
                    return DownloadResult(
                        success=False,
                        message="Download service returned an error",
                        title=None
                    )

                # Extract file_id from response
                file_id = data.get("file_id")
                if not file_id:
                    logger.error(f"No file_id returned from FastSaver API for shortcode {shortcode}")
                    return DownloadResult(
                        success=False,
                        message="Download service did not return valid file",
                        title=None
                    )

                # Get title and duration from YouTube page
                title = await self._get_video_title_from_youtube(shortcode)
                logger.info(f"Title from YouTube page: {repr(title)}")

                # If no title, use shortcode as fallback
                if not title:
                    title = f"YouTube Media {shortcode}"

                # Get duration from YouTube page
                duration_seconds = await self._get_video_duration_from_youtube(shortcode)
                logger.info(f"Duration from YouTube page: {duration_seconds} seconds")

                logger.info(f"FastSaver download successful: file_id={file_id}, title: {title}, duration: {duration_seconds}")

                return DownloadResult(
                    success=True,
                    message="Media downloaded successfully via FastSaver API",
                    file_path=None,  # No local file path since we use file_id
                    title=title,
                    duration=duration_seconds,  # Include duration in seconds
                    telegram_file_id=file_id
                )

        except httpx.RequestError as e:
            logger.error(f"Request error during FastSaver download for shortcode {shortcode}: {e}")
            return DownloadResult(
                success=False,
                message="Network connection error occurred",
                title=None
            )
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error during FastSaver download for shortcode {shortcode}: {e.response.status_code} - {e}")
            return DownloadResult(
                success=False,
                message="Download service temporarily unavailable",
                title=None
            )
        except Exception as e:
            logger.error(f"Unexpected error during FastSaver download for shortcode {shortcode}: {e}")
            return DownloadResult(
                success=False,
                message="Download failed due to unexpected error",
                title=None
            )

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def _get_video_info_from_fastsaver(self, video_id: str) -> Optional[dict]:
        """Get video information from FastSaver API get-info endpoint."""
        try:
            # Construct YouTube URL for the video
            youtube_url = f"https://youtu.be/{video_id}"

            # Prepare API request
            params = {
                "url": youtube_url,
                "token": self.api_token
            }

            logger.info(f"Getting video info from FastSaver API for video_id: {video_id}")

            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(self.get_info_url, params=params)
                response.raise_for_status()

                data = response.json()

                # Check for API errors
                if data.get("error", False):
                    api_error_msg = data.get('message', 'Unknown error')
                    logger.error(f"FastSaver get-info API returned error for video_id {video_id}: {api_error_msg}")
                    return None

                logger.info(f"Successfully got video info from FastSaver API for video_id: {video_id}")
                return data

        except Exception as e:
            logger.warning(f"Failed to get video info from FastSaver API for {video_id}: {e}")

        return None

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def _get_video_title_from_youtube(self, video_id: str) -> Optional[str]:
        """Get video title using FastSaver API get-info endpoint."""
        try:
            # Use FastSaver API get-info endpoint instead of scraping YouTube
            video_info = await self._get_video_info_from_fastsaver(video_id)
            if video_info:
                title = video_info.get("title")
                if title:
                    logger.info(f"Got title from FastSaver API: {repr(title)}")
                    return title

            logger.warning(f"Could not get title from FastSaver API for video_id: {video_id}")
            return None

        except Exception as e:
            logger.warning(f"Failed to get video title for {video_id}: {e}")

        return None  # Return None if title not found

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def _get_video_duration_from_youtube(self, video_id: str) -> Optional[int]:
        """Get video duration using FastSaver API get-info endpoint."""
        try:
            # Use FastSaver API get-info endpoint instead of scraping YouTube
            video_info = await self._get_video_info_from_fastsaver(video_id)
            if video_info:
                duration = video_info.get("duration")
                if duration:
                    # Convert duration string to seconds if needed
                    try:
                        # Handle formats like "3:45" or "1:23:45"
                        if isinstance(duration, str):
                            time_parts = duration.split(":")
                            if len(time_parts) == 2:  # MM:SS
                                duration_seconds = int(time_parts[0]) * 60 + int(time_parts[1])
                            elif len(time_parts) == 3:  # HH:MM:SS
                                duration_seconds = int(time_parts[0]) * 3600 + int(time_parts[1]) * 60 + int(time_parts[2])
                            else:
                                duration_seconds = int(duration) if duration.isdigit() else None
                        elif isinstance(duration, (int, float)):
                            duration_seconds = int(duration)
                        else:
                            duration_seconds = None

                        if duration_seconds:
                            logger.info(f"Got duration from FastSaver API: {duration_seconds} seconds")
                            return duration_seconds
                    except (ValueError, AttributeError):
                        logger.warning(f"Could not parse duration from FastSaver API: {duration}")

            logger.warning(f"Could not get duration from FastSaver API for video_id: {video_id}")
            return None

        except Exception as e:
            logger.warning(f"Failed to get video duration for {video_id}: {e}")

        return None  # Return None if duration not found
