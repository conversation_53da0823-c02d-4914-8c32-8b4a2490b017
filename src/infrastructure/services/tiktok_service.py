"""TikTok service implementation."""
import logging
from typing import Optional
from urllib.parse import quote
import httpx

from src.domain.interfaces.services import ITikTokService
from src.domain.value_objects.url import Url
from src.domain.entities.media_item import MediaItem, MediaType
from src.infrastructure.config.settings import settings
from src.infrastructure.utils.retry_decorator import retry_async

logger = logging.getLogger(__name__)


class TikTokService(ITikTokService):
    """TikTok service implementation using FastSaver API."""

    def __init__(self):
        self.api_base_url = settings.tiktok.api_base_url
        self.api_token = settings.tiktok.api_token

        if not self.api_token:
            logger.warning("TikTok API token not configured. Set TIKTOK_API_TOKEN environment variable.")

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def get_media_info(self, url: Url) -> MediaItem:
        """Get media information from TikTok URL."""
        if not self.api_token:
            raise Exception("TikTok API token not configured. Please set TIKTOK_API_TOKEN environment variable.")

        try:
            encoded_url = quote(url.value, safe='')
            api_url = f"{self.api_base_url}?url={encoded_url}&token={self.api_token}"

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(api_url)
                response.raise_for_status()

                data = response.json()
                logger.info(f"TikTok API response: {data}")

                if data.get("error", True):
                    error_msg = data.get("message", "Unknown error from TikTok API")
                    logger.error(f"TikTok API error: {error_msg}")
                    raise Exception(f"TikTok API error: {error_msg}")

                # Determine media type based on response
                media_type = MediaType.VIDEO  # TikTok is primarily video content

                return MediaItem(
                    url=url.value,
                    media_type=media_type,
                    download_url=data.get("download_url"),
                    caption=data.get("caption"),
                    duration=int(data.get("duration", 0)) if data.get("duration") else None,
                    width=data.get("width"),
                    height=data.get("height"),
                    thumbnail_url=data.get("thumb"),
                    hosting=data.get("hosting"),
                    music_url=data.get("music")
                )

        except httpx.RequestError as e:
            logger.error(f"Request error getting TikTok media info: {e}")
            raise Exception("Failed to connect to TikTok service")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error getting TikTok media info: {e.response.status_code} - {e}")
            raise Exception("TikTok service temporarily unavailable")
        except Exception as e:
            logger.error(f"Error getting TikTok media info: {e}")
            raise

    @retry_async(max_attempts=3, delay=1.0, backoff=2.0)
    async def download_media(self, media_item) -> bytes:
        """Download media content."""
        try:
            download_url = None

            if hasattr(media_item, 'download_url'):
                download_url = media_item.download_url
            elif isinstance(media_item, dict):
                download_url = media_item.get('download_url')
            else:
                raise ValueError("Invalid media item format")

            if not download_url:
                raise Exception("No download URL available")

            # Add headers to mimic a browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': '*/*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'video',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'cross-site',
            }

            async with httpx.AsyncClient(timeout=120.0, headers=headers, follow_redirects=True) as client:
                response = await client.get(download_url)
                response.raise_for_status()

                logger.info(f"Downloaded {len(response.content)} bytes from TikTok")
                return response.content

        except httpx.RequestError as e:
            logger.error(f"Request error downloading TikTok media: {e}")
            raise Exception("Failed to download TikTok media")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error downloading TikTok media: {e.response.status_code} - {e}")
            raise Exception("TikTok media download failed")
        except Exception as e:
            logger.error(f"Error downloading TikTok media: {e}")
            raise

    async def download_music(self, music_url: str) -> bytes:
        """Download music/audio from TikTok."""
        try:
            if not music_url:
                raise Exception("No music URL available")

            # Add headers to mimic a browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': '*/*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'audio',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'cross-site',
            }

            async with httpx.AsyncClient(timeout=120.0, headers=headers, follow_redirects=True) as client:
                response = await client.get(music_url)
                response.raise_for_status()

                logger.info(f"Downloaded {len(response.content)} bytes of TikTok music")
                return response.content

        except httpx.RequestError as e:
            logger.error(f"Request error downloading TikTok music: {e}")
            raise Exception("Failed to download TikTok music")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error downloading TikTok music: {e.response.status_code} - {e}")
            raise Exception("TikTok music download failed")
        except Exception as e:
            logger.error(f"Error downloading TikTok music: {e}")
            raise

    async def download_thumbnail(self, thumbnail_url: str) -> bytes:
        """Download thumbnail from TikTok."""
        try:
            if not thumbnail_url:
                raise Exception("No thumbnail URL available")

            # Add headers to mimic a browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'image',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'cross-site',
            }

            async with httpx.AsyncClient(timeout=60.0, headers=headers, follow_redirects=True) as client:
                response = await client.get(thumbnail_url)
                response.raise_for_status()

                logger.info(f"Downloaded {len(response.content)} bytes of TikTok thumbnail")
                return response.content

        except httpx.RequestError as e:
            logger.error(f"Request error downloading TikTok thumbnail: {e}")
            raise Exception("Failed to download TikTok thumbnail")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error downloading TikTok thumbnail: {e.response.status_code} - {e}")
            raise Exception("TikTok thumbnail download failed")
        except Exception as e:
            logger.error(f"Error downloading TikTok thumbnail: {e}")
            raise
