"""Error notification service implementation."""
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError

from src.domain.interfaces.error_notification_service import IErrorNotificationService
from src.infrastructure.config.settings import settings


logger = logging.getLogger(__name__)


class ErrorNotificationService(IErrorNotificationService):
    """Error notification service implementation."""

    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.bot = Bot(token=bot_token)
        self.admin_chat_id = settings.admin.chat_id
        self.notifications_enabled = settings.admin.enable_error_notifications

    async def notify_admin_error(
        self,
        error_message: str,
        error_type: str,
        context: Optional[dict] = None,
        user_chat_id: Optional[str] = None,
        response_details: Optional[Dict[str, Any]] = None,
        execution_time: Optional[float] = None,
        stack_trace: Optional[str] = None
    ) -> None:
        """Notify admin about an error with detailed information."""
        if not self.notifications_enabled or not self.admin_chat_id:
            logger.warning("Admin error notifications are disabled or admin chat ID not configured")
            return

        try:
            # Format error message for admin
            admin_message = self._format_detailed_admin_error_message(
                error_message=error_message,
                error_type=error_type,
                context=context,
                user_chat_id=user_chat_id,
                response_details=response_details,
                execution_time=execution_time,
                stack_trace=stack_trace
            )

            # Send message to admin
            await self.bot.send_message(
                chat_id=self.admin_chat_id,
                text=admin_message,
                parse_mode='HTML'
            )

            logger.info(f"Detailed error notification sent to admin: {error_type}")

        except TelegramError as e:
            logger.error(f"Failed to send error notification to admin: {e}")
        except Exception as e:
            logger.error(f"Unexpected error while sending admin notification: {e}")

    def get_user_friendly_message(self, error_type: str, original_error: str) -> str:
        """Get user-friendly error message in Uzbek."""
        error_messages = {
            'INSTAGRAM_API_ERROR': "Instagram havolasi ishlamayapti. Boshqa havola sinab ko'ring.",
            'YOUTUBE_API_ERROR': "YouTube havolasi ishlamayapti. Boshqa havola sinab ko'ring.",
            'MUSIC_SEARCH_ERROR': "Musiqa topilmadi. Boshqa nom bilan qidiring.",
            'DOWNLOAD_ERROR': "Yuklab bo'lmadi. Qaytadan urinib ko'ring.",
            'TELEGRAM_ERROR': "Yuborishda xatolik. Qaytadan urinib ko'ring.",
            'VALIDATION_ERROR': "Noto'g'ri havola. To'g'ri havola yuboring.",
            'FILE_NOT_FOUND': "Fayl topilmadi. Boshqa havola sinab ko'ring.",
            'FILE_SIZE_ERROR': "Fayl juda katta. Kichikroq fayl tanlang.",
            'NETWORK_ERROR': "Internet bilan muammo. Keyinroq urinib ko'ring.",
            'DATABASE_ERROR': "Tizimda xatolik. Keyinroq urinib ko'ring.",
            'CACHE_ERROR': "Tizimda xatolik. Keyinroq urinib ko'ring.",
            'AUTHENTICATION_ERROR': "Ruxsat xatoligi. Qaytadan urinib ko'ring.",
            'RATE_LIMIT_ERROR': "Juda tez yuborayapsiz. Biroz kuting.",
            'UNSUPPORTED_FORMAT': "Bu format qo'llab-quvvatlanmaydi.",
            'INVALID_URL': "Noto'g'ri havola. To'g'ri havola yuboring.",
            'SERVER_ERROR': "Tizimda xatolik. Keyinroq urinib ko'ring."
        }

        return error_messages.get(error_type, "Xatolik yuz berdi. Keyinroq urinib ko'ring.")

    def _format_detailed_admin_error_message(
        self,
        error_message: str,
        error_type: str,
        context: Optional[dict],
        user_chat_id: Optional[str],
        response_details: Optional[Dict[str, Any]],
        execution_time: Optional[float],
        stack_trace: Optional[str]
    ) -> str:
        """Format detailed error message for admin notification."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        message_parts = [
            f"🚨 <b>Bot Error Notification</b>",
            f"",
            f"⏰ <b>Time:</b> {timestamp}",
            f"🔴 <b>Error Type:</b> {error_type}",
            f"💬 <b>Error Message:</b> {error_message}",
        ]

        if user_chat_id:
            message_parts.append(f"👤 <b>User Chat ID:</b> {user_chat_id}")

        if execution_time is not None:
            message_parts.append(f"⏱️ <b>Execution Time:</b> {execution_time:.2f}s")

        # Response Details Section
        if response_details:
            message_parts.append(f"")
            message_parts.append(f"📥 <b>Response Details:</b>")
            
            if 'status_code' in response_details:
                message_parts.append(f"  • Status Code: {response_details['status_code']}")
            
            if 'response_body' in response_details and response_details['response_body']:
                # Truncate response body if too long
                response_body = str(response_details['response_body'])
                if len(response_body) > 200:
                    response_body = response_body[:200] + "..."
                message_parts.append(f"  • Response Body: {response_body}")

        # Context Section
        if context:
            message_parts.append(f"")
            message_parts.append(f"📋 <b>Context:</b>")
            for key, value in context.items():
                if isinstance(value, str) and len(value) > 100:
                    value = value[:100] + "..."
                message_parts.append(f"  • {key}: {value}")

        # Stack Trace Section (if available and not too long)
        if stack_trace:
            message_parts.append(f"")
            message_parts.append(f"🔍 <b>Stack Trace:</b>")
            # Limit stack trace to first 10 lines to avoid message length issues
            stack_lines = stack_trace.split('\n')[:10]
            for line in stack_lines:
                message_parts.append(f"  {line}")
            if len(stack_trace.split('\n')) > 10:
                message_parts.append(f"  ... (truncated)")

        return "\n".join(message_parts)

    def _format_admin_error_message(
        self,
        error_message: str,
        error_type: str,
        context: Optional[dict],
        user_chat_id: Optional[str]
    ) -> str:
        """Format error message for admin notification (legacy method)."""
        return self._format_detailed_admin_error_message(
            error_message=error_message,
            error_type=error_type,
            context=context,
            user_chat_id=user_chat_id,
            response_details=None,
            execution_time=None,
            stack_trace=None
        )

    def classify_error(self, error: Exception) -> str:
        """Classify error type based on exception."""
        error_str = str(error).lower()
        error_type = type(error).__name__

        # Network related errors
        if any(keyword in error_str for keyword in ['connection', 'timeout', 'network', 'unreachable']):
            return 'NETWORK_ERROR'
        
        # File related errors
        if 'file not found' in error_str or isinstance(error, FileNotFoundError):
            return 'FILE_NOT_FOUND'
        
        # Size related errors
        if any(keyword in error_str for keyword in ['size', 'large', 'limit', 'exceed']):
            return 'FILE_SIZE_ERROR'
        
        # Validation errors
        if any(keyword in error_str for keyword in ['validation', 'invalid', 'format']) or isinstance(error, ValueError):
            return 'VALIDATION_ERROR'
        
        # API specific errors
        if 'instagram' in error_str:
            return 'INSTAGRAM_API_ERROR'
        elif 'youtube' in error_str:
            return 'YOUTUBE_API_ERROR'
        elif 'music' in error_str or 'search' in error_str:
            return 'MUSIC_SEARCH_ERROR'
        elif 'telegram' in error_str:
            return 'TELEGRAM_ERROR'
        elif 'download' in error_str:
            return 'DOWNLOAD_ERROR'
        elif 'database' in error_str:
            return 'DATABASE_ERROR'
        elif 'cache' in error_str:
            return 'CACHE_ERROR'
        elif 'auth' in error_str or 'token' in error_str:
            return 'AUTHENTICATION_ERROR'
        elif 'rate' in error_str or 'limit' in error_str:
            return 'RATE_LIMIT_ERROR'
        elif 'url' in error_str or 'link' in error_str:
            return 'INVALID_URL'
        
        # Default to server error
        return 'SERVER_ERROR'

    def extract_response_details(self, response) -> Dict[str, Any]:
        """Extract response details from FastAPI response object."""
        try:
            details = {
                'status_code': getattr(response, 'status_code', None)
            }
            
            # Try to get response body
            try:
                if hasattr(response, 'body'):
                    body = response.body
                    if body:
                        details['response_body'] = body.decode('utf-8') if isinstance(body, bytes) else body
            except Exception:
                details['response_body'] = "Unable to read response body"
            
            return details
        except Exception as e:
            logger.error(f"Error extracting response details: {e}")
            return {'error': f"Failed to extract response details: {e}"}
